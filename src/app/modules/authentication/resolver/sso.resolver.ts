import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot, } from '@angular/router';
import { Store } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { Navigate } from '@ngxs/router-plugin';
import { CookieTypes, deleteCookie, getCookie, setCookie } from '../../../util/cookie.util';
import { LoginWithSSOTokenAction, SetCATSignInCode, UpdateLanguageCodeAction } from '../../authentication/state/login/login.actions';
import { LoginState } from '../state/login/login.state';
import { ssoRedirectPats } from '../../../shared/enum/sso-pats.enum';


@Injectable({
  providedIn: 'root',
})
export class SsoResolver implements Resolve<boolean> {
  constructor(private readonly store: Store) {}

  resolve(
    { queryParams: { code, token, email, ru, action, language } }: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): any {
    if (token) {  // SAML SSO return token
      if (action !== 'resolve') {
        return this.store.dispatch([]);
      }
      if (language) {
        this.store.dispatch(new UpdateLanguageCodeAction(language));
      }

      return this.store.dispatch(new LoginWithSSOTokenAction(token));
    } else if (ru && this.isSSORedirect(ru)) { // start Cat Sign-in CWS login
      const lang = this.store.selectSnapshot(LoginState.language) || language || 'tr';

      setCookie(CookieTypes.SSOTARGETPROJECT, ru?.replace('{{LANG}}', lang)?.replace('{{LANG}}', lang), { expireDays: 1 });

      // if already signed in
      if (getCookie('CATSignInCode')) {
        console.log('Already loggedin');

        this.redirectToTargetProject();
        return true;
      }

      window.open(environment.signinCatUrl, '_self');
    } else if (email || action === 'login') { // Start SALM login
      const lang = this.store.selectSnapshot(LoginState.language) || language || 'tr';

      const ssoUrl =
        environment.api +
        environment.ssoUrl
          .replace('DYNAMICLANG', lang)
        + (email ? `&email=${email}` : '');

      window.open(ssoUrl, '_self');
    } else if (code) { // HANDLE CAT return with code
      this.store.dispatch(new SetCATSignInCode(code));

      this.redirectToTargetProject();
    } else {
      this.store.dispatch(new Navigate(['/']));
    }
  }


  redirectToTargetProject() {
    const redirectPage = getCookie(CookieTypes.SSOTARGETPROJECT);
    if (this.isSSORedirect(redirectPage)) {
      console.log('REDIRECTING', redirectPage);
      deleteCookie(CookieTypes.SSOTARGETPROJECT);
      window.open(redirectPage, '_self');
      return true;
    }
    // this.store.dispatch(new Navigate(['/']));
    return false;
  }

  private isSSORedirect(ru: any) {
    return ru && ssoRedirectPats.find(url => ru?.startsWith(url));
  }
}
