<div
  *ngIf="!(pccOrVlinkSurveyLoading$ | async) && !successSend && pccOrLinkSurvey"
  class="p-4"
  style="height: calc(100vh - 64px); overflow: auto"
>
  <h3 class="text-center mb-2">{{ pccOrLinkSurvey?.name }}</h3>
  <p class="text-center mb-4">{{ pccOrLinkSurvey?.greetings }}</p>

  <form (submit)="onSubmitForm()" [formGroup]="form">
    <ng-container
      *ngFor="let question of pccOrLinkSurvey?.surveyQuestions; let i = index"
    >
    <ng-container *ngIf="shouldDisplayQuestion(question)">
      <div class="d-flex center">
        <h4>{{ i + 1 }}.</h4>
        <label class="ml-2 question-label">{{ question.question }}</label>
      </div>
      <ng-container *ngIf="question.questionTypeDesc === formType.EmojiRating">
        <div class="form-group emoji-options">
          <button
            type="button"
            class="btn emoji-btn rate-box"
            *ngFor="let opt of question.questionOptions; let rateIndex = index"
            [ngStyle]="{
              'background-color': getSurveyRateColorByRate(rateIndex + 1)
            }"
            [class.selected]="
              form.get(question.questionId)?.value === opt.value
            "
            (click)="form.get(question.questionId)?.setValue(opt.value)"
          >
            <img
              class="rate-icon"
              [src]="iconMap[opt.text]"
              width="20"
              height="20"
            />
          </button>
        </div>
        <div
          [ngClass]="{
            'd-block': isShowError(form.get(question.questionId))
          }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.get(question.questionId)) | translate }}
        </div>
      </ng-container>
      <ng-container
        *ngIf="question.questionTypeDesc === formType.MultipleSelectCheckbox"
      >
        <div class="form-group">
          <div class="form-check" *ngFor="let opt of question.questionOptions">
            <input
              class="form-check-input custom-checkbox"
              type="checkbox"
              [value]="opt.value"
              [checked]="
                form.get(question.questionId)?.value?.includes(opt.value)
              "
              (change)="onCheckboxChange($event, question.questionId)"
              [id]="opt.id"
            />
            <label class="form-check-label" [for]="opt.id">{{
              opt.text
            }}</label>
          </div>

          <div
            [ngClass]="{
              'd-block': isShowError(form.get(question.questionId))
            }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.get(question.questionId)) | translate }}
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="question.questionTypeDesc === formType.freetext">
        <div class="form-group">
          <textarea
            appInputMaxLength
            [name]="'Description'"
            [placeholder]="'_description' | translate"
            [rows]="5"
            class="form-control form-control"
            formControlName="{{ question.questionId }}"
            minlength="3"
            style="resize: none"
          ></textarea>
          <div
            [ngClass]="{
              'd-block':
                !form.get(question.questionId).valid &&
                form.get(question.questionId).touched
            }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.get(question.questionId)) | translate }}
          </div>
        </div>
      </ng-container>
    </ng-container>
    </ng-container>

    <button class="btn btn-sm btn-warning text-white w-100 mt-3" type="submit">
      {{ "_send" | translate }}
    </button>
  </form>
</div>
<app-loader
  [show]="(pccOrVlinkSurveyLoading$ | async) || sendLoading"
></app-loader>
<div *ngIf="successSend" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="h3 mb-5">{{ "_successfully_send_survey_form" | translate }}</div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="back()"
    >
      {{ "_return_back" | translate }}
    </div>
  </div>
</div>
