import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  HostListener,
  OnInit,
} from '@angular/core';
import {
  GetPccOrVlinkSurvey,
  HeaderStatusAction,
} from '../state/customer/customer.actions';
import { LogService } from 'src/app/shared/service/log.service';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { CustomerService } from '../service/customer.service';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { CustomerState } from '../state/customer/customer.state';
import { takeUntil } from 'rxjs/operators';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import {
  getFormErrorMessage,
  isShowFormError,
  validateAllFormFields,
} from 'src/app/util/form-error.util';
import { SurveyService } from '../service/survey.service';

export enum FormType {
  EmojiRating = 'EmojiRating',
  MultipleSelectCheckbox = 'MultipleSelectCheckbox',
  freetext = 'freetext',
}

@Component({
  selector: 'app-pcc-vlink-survey',
  templateUrl: './pcc-vlink-survey.component.html',
  styleUrls: ['./pcc-vlink-survey.component.scss'],
})
export class PccVlinkSurveyComponent implements OnInit, AfterViewChecked {
  protected subscriptions$: Subject<boolean> = new Subject();

  @Select(CustomerState.pccOrVlinkSurvey)
  pccOrVlinkSurvey$: Observable<any>;

  @Select(CustomerState.pccOrVlinkSurveyLoading)
  pccOrVlinkSurveyLoading$: Observable<boolean>;

  formType = FormType;

  pccOrLinkSurvey;
  successSend = false;
  sendLoading = false;
  surveyPosition: '';

  form: FormGroup = new FormGroup({});

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  iconMap = {
    angry_face: `${environment.assets}/bad-rate-white.png`,
    happy_face: `${environment.assets}/good-rate-white.png`,
    ok_face: `${environment.assets}/smile-rate-white.png`,
    neutral_face: `${environment.assets}/middle-rate-white.png`,
    sad_face: `${environment.assets}/average-rate-white.png`,
  };

  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly translate: TranslatePipe,
    private readonly router: Router,
    private readonly log: LogService,
    private readonly surveyService: SurveyService,
    private readonly activatedRoute: ActivatedRoute,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  @HostListener('window:resize', ['$event'])
  onResize() {
    setTimeout(() => {
      this.scrollActiveElementIntoView();
    }, 100);
  }

  scrollActiveElementIntoView() {
    const activeElement = document.activeElement as HTMLElement;

    if (
      activeElement &&
      ['INPUT', 'TEXTAREA'].includes(activeElement.tagName)
    ) {
      activeElement.scrollIntoView();
    }
  }

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  ngOnInit() {
    this.successSend = false;
    this.activatedRoute.queryParams.subscribe((params) => {
      const position = params['position'] || 'VisionLinkEnd';
      this.surveyPosition = position;
      //VisionLinkEnd REQUEST ADD TO FRAME MESSAGE SERVICE
      if (position !== 'VisionLinkEnd') {
        this.store.dispatch(
          new GetPccOrVlinkSurvey({ position, getDetails: true })
        );
      }
    });
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
      })
    );

    this.pccOrVlinkSurvey$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((survey) => {
        this.pccOrLinkSurvey = survey;
        this.buildDynamicForm(survey?.surveyQuestions || []);
      });
  }

  private buildDynamicForm(questions: any[]) {
    const group: { [key: string]: FormControl } = {};

    questions.forEach((q) => {
      const validators = q.isRequired ? [Validators.required] : [];
      const initialValue =
        q.questionTypeDesc === this.formType.MultipleSelectCheckbox ? [] : '';
      group[q.questionId] = new FormControl(initialValue, validators);
    });

    this.form = new FormGroup(group);
  }

  shouldDisplayQuestion(question: any): boolean {
    if (!question.conditionalQuestionId) return true;

    const parentValue = this.form.get(question.conditionalQuestionId)?.value;

    if (Array.isArray(parentValue)) {
      return parentValue?.some((val) =>
        question.conditionalQuestionValues.includes(val)
      );
    }

    return question.conditionalQuestionValues.includes(parentValue);
  }

  onSubmitForm() {
    this.form.markAllAsTouched(); // hata mesajlarının görünmesi için

    const visibleQuestions = this.pccOrLinkSurvey?.surveyQuestions.filter((q) =>
      this.shouldDisplayQuestion(q)
    );

    let hasInvalid = false;

    for (const q of visibleQuestions) {
      const control = this.form.get(q.questionId);
      if (control?.invalid) {
        hasInvalid = true;
        break;
      }
    }

    if (hasInvalid) {
      console.log('enes');
      return;
    }

    this.sendLoading = true;

    const value = this.form.value;
    const answers = [];

    for (const question of visibleQuestions) {
      const answerValue = value[question.questionId];

      if (
        answerValue === null ||
        answerValue === undefined ||
        answerValue === '' ||
        (Array.isArray(answerValue) && answerValue.length === 0)
      ) {
        continue;
      }

      if (Array.isArray(answerValue)) {
        for (const val of answerValue) {
          answers.push({
            questionId: question.questionId,
            answer: val,
            comment: '',
          });
        }
      } else {
        answers.push({
          questionId: question.questionId,
          answer: answerValue,
          comment: '',
        });
      }
    }

    const payload = {
      surveyId: this.pccOrLinkSurvey?.surveyId,
      surveyPosition: this.surveyPosition,
      additionalData: '',
      relatedContextId: '',
      answers,
    };

    this.surveyService.sendPccOrVlinkSurveyAnswer(payload).subscribe({
      next: () => {
        this.successSend = true;
        this.sendLoading = false;
        this.log
          .log('SURVEY', this.surveyPosition, {
            payload,
          })
          .subscribe();
      },
      error: (err) => {
        console.error('Survey submit failed:', err);
        this.sendLoading = false;
      },
    });
  }

  onCheckboxChange(event: Event, questionId: string) {
    const input = event.target as HTMLInputElement;
    const current = this.form.get(questionId)?.value || [];

    if (input.checked) {
      this.form.get(questionId)?.setValue([...current, input.value]);
    } else {
      this.form
        .get(questionId)
        ?.setValue(current.filter((v: string) => v !== input.value));
    }
  }

  getSurveyRateColorByRate(rate: number): string {
    const colors = ['#921f1d', '#c83825', '#db8e2c', '#acca7f', '#9cc390'];

    return colors[rate - 1];
  }

  back() {
    this.router.navigate(['dashboard']);
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
