import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';
import { CustomerComponent } from '../../container/customer/customer.component';
import { AppDashboardComponent } from './container/app-dashboard/app-dashboard.component';
import { CatalogComponent } from './container/catalog/catalog.component';
import { IframeViewComponent } from './container/iframe-view/iframe-view.component';
import { matchPathsStartingWith } from '../../util/url-matcher.util';
import { OrderFormComponent } from '../catalog/component/order-form/order-form.component';
import { CatalogRedirectComponent } from '../catalog/component/catalog-redirect/catalog-redirect.component';
import { IncomingRepeaterComponent } from './container/incoming-repeater/incoming-repeater.component';
import { PccVlinkSurveyComponent } from './pcc-vlink-survey/pcc-vlink-survey.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'dashboard',
    canActivate: [PermissionGuard],
  },
  {
    path: '',
    canActivate: [PermissionGuard],
    component: CustomerComponent,
    children: [
      {
        path: 'dashboard',
        component: AppDashboardComponent,
        canActivate: [PermissionGuard],
      },
      {
        path: 'promotion',
        canActivate: [PermissionGuard],
        loadChildren: () =>
          import('./../promotion/promotion.module').then(
            (m) => m.PromotionModule
          ),
      },
      {
        matcher: matchPathsStartingWith(['catalog']),
        // path: 'catalog/:id',
        component: CatalogComponent,
        canActivate: [PermissionGuard],
      },
      {
        matcher: matchPathsStartingWith(['incoming-repeater']),
        component: IncomingRepeaterComponent,
        canActivate: [PermissionGuard],
      },
      {
        path: 'redirect-catalog/:top',
        component: CatalogRedirectComponent,
        canActivate: [PermissionGuard],
      },
      {
        path: 'redirect-catalog/:top/:id',
        component: CatalogRedirectComponent,
        canActivate: [PermissionGuard],
      },
      {
        path: 'get-offer',
        component: OrderFormComponent,
        canActivate: [PermissionGuard],
      },
      {
        path: 'module/:id',
        component: IframeViewComponent,
        canActivate: [PermissionGuard],
        data: {
          reuseRoute: false,
        },
      },
      {
        path: 'pcc-vlink-survey',
        component: PccVlinkSurveyComponent,
        canActivate: [PermissionGuard],
      },
      {
        path: 'contact',
        canActivate: [PermissionGuard],
        loadChildren: () =>
          import('./container/contact/contact.module').then(
            (m) => m.ContactModule
          ),
      },
      {
        path: 'settings',
        canActivate: [PermissionGuard],
        loadChildren: () =>
          import('./container/settings/settings.module').then(
            (m) => m.SettingsModule
          ),
      },
      {
        path: 'notifications',
        loadChildren: () =>
          import('../notification/notification.module').then(
            (m) => m.NotificationModule
          ),
      },

      {
        path: 'one-funnel',
        canActivate: [PermissionGuard],
        loadChildren: () =>
          import('../one-funnel/one-funnel.module').then(
            (m) => m.OneFunnelModule
          ),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomerRoutingModule {}
