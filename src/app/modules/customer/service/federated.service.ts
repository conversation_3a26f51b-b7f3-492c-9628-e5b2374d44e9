import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { SSORedirectKeys } from '../../../shared/enum/sso-pats.enum';
import { FrameMessageService } from '../../../core/service/frame-message.service';
import { Store } from '@ngxs/store';
import { ClearCATSignInCode } from '../../authentication/state/login/login.actions';
import { BackGroundFrameStatusAction } from '../../../shared/state/common/common.actions';
import { LogService } from '../../../shared/service/log.service';

@Injectable({
  providedIn: 'root',
})
export class FederatedService {
  constructor(
    private readonly frameMessageService: FrameMessageService,
    private readonly store: Store,
    private readonly logService: LogService,
  ) {}


  startBackgroundFrame(reloginCount) {
    const url = environment.appUrl + '/auth/sso?ru=' + encodeURIComponent(SSORedirectKeys.partsCatCom);
    this.frameMessageService.startBackgroundFrame({
      url
    });
    this.store.dispatch(new BackGroundFrameStatusAction('started'));
    this.clearCatSingIn();
    this.logService.log('BACKGROUND_FRAME', 'REQUEST_START', {
      reloginCount,
    }).subscribe();
  }

  stopBackgroundFrame() {
    this.frameMessageService.stopBackgroundFrame();
    this.logService.log('BACKGROUND_FRAME', 'REQUEST_STOP', {}).subscribe();
  }

  clearCatSingIn() {
    this.store.dispatch(new ClearCATSignInCode());
  }
}
