import { Injectable } from '@angular/core';
import { UserState } from '../state/user/user.state';
import {
  ChangeCustomerAction,
  FindAndChangeCustomerAction,
  UpdateUserLoadingAction
} from '../state/user/user.actions';
import { Store } from '@ngxs/store';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../../../environments/environment';
import { UpdateHeaderCompaniesAction } from '../../authentication/state/login/login.actions';
import { TranslateService } from '@ngx-translate/core';
import { ModuleCodeEnum } from '../../../shared/enum/module-code.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { CustomerService } from './customer.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { CustomerState } from '../state/customer/customer.state';
import { Location } from '@angular/common';
import { v4 as uuidv4 } from 'uuid';
import { IframeStateModel } from '../state/iframe/iframe.state';
import { filter, map, take } from 'rxjs/operators';
import { ModalService } from '../../../shared/service/modal.service';
import { throwError } from 'rxjs';
import {
  CatQRAction,
  CustomerDetailAction,
  GetOTTAction,
  HeaderStatusAction
} from '../state/customer/customer.actions';
import { OpenLinkData } from '../../../core/interfaces/core-types.interface';
import {
  BorusanUserGetRelatedPersonsAction,
  BorusanUserSetSelectedCustomerAction
} from '../../borusan-user/state/borusan-user.action';
import { BorusanUserState } from '../../borusan-user/state/borusan-user.state';
import {
  HasPermissionsService
} from 'src/app/export/file-upload/permissions/service/has-permissions.service';
import { PermissionEnum } from '../../definition/enum/permission.enum';
import { PageOpenedAction } from '../../../shared/state/common/common.actions';
import { PagesEnum } from '../../../shared/enum/pages.enum';
import { CommonState } from '../../../shared/state/common/common.state';
import { LoginState } from '../../authentication/state/login/login.state';
import { SettingsState } from '../../../shared/state/settings/settings.state';
import { systemFeature } from '../../../util/system-feature.util';
import { SSORedirectKeys } from '../../../shared/enum/sso-pats.enum';
import { LogService } from '../../../shared/service/log.service';
import { getCookie } from '../../../util/cookie.util';

@Injectable({
  providedIn: 'root',
})
export class CustomerModuleService {
  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly translateService: TranslateService,
    private readonly frameMessageService: FrameMessageService,
    private readonly customerService: CustomerService,
    private readonly location: Location,
    private readonly modalService: ModalService,
    private readonly permissionService: HasPermissionsService,
  ) { }

  findAndNavigate(customerNumber, moduleCode, overrides: any = {}, options: any = {}) {
    const customer = this.findCustomerWithModule(customerNumber, moduleCode);
    if (!customer) {
      this.modalService.errorModal({
        message: '_not_found_customer',
        translate: true
      });

      if (this.router.url.includes('notifications')) {
        this.router.navigate(['notifications']);
      }

      console.log('ERROR: customer with module not found', { customerNumber, moduleCode });
      return;
    }
    const module = customer.modules.find((item) => item.code === moduleCode || !moduleCode);
    if (module?.isMaintenance) {
      return this.maintenanceModeProcess(module);
    }
    this.store.dispatch(new ChangeCustomerAction(customer));
    overrides.url = overrides?.relativeUrl ? this.buildUrl(overrides.relativeUrl) : module.url;
    this.store.dispatch(new UpdateHeaderCompaniesAction(module.headerCompanies));

    if (moduleCode === ModuleCodeEnum.SparePart) {
      return this.openSparePartModule(customerNumber, options);
    }

    if (module?.tags?.find(tag => tag === 'externalview')) {
      this.openExternalModule(module);
      return;
    }

    const iframeAction = {
      url: module.url,
      active: true,
      closeButton: true,
      pageTitle: module.name,
      ...overrides
    };
    // console.log('iframe:::: ', iframeAction);

    const extras: any = { state: { iframeAction } };
    if (overrides?.replaceUrl) {
      extras.replaceUrl = overrides.replaceUrl;
    }
    this.router.navigate(['module', module.id], extras).then();

    return module;
  }

  findCustomerWithModule(customerNumber, moduleCode: ModuleCodeEnum) {
    const customers = this.store.selectSnapshot(UserState.customers);
    // console.log('customers', customers);
    const trimmedNumber = customerNumber?.replace(/^0+/, '');
    return customers.filter(c => !c.passive)
      .find((group) => {
        const customersFilterNumber = group.customer?.customerNumber?.replace(/^0+/, '');
        return customersFilterNumber === trimmedNumber &&
          group.modules.find((item) => {
            return item.code === moduleCode || !moduleCode;
          });
      });
  }

  private buildUrl(relativeUrl: any) {
    const sp = [
      environment.frameUrl,
      relativeUrl
    ];

    return sp.join('/');
  }

  handleOpenModuleLinkEvent(data: OpenLinkData) {
    // console.log('handleOpenModuleLinkEvent', data);
    const customerNumber = data?.customerNumber
      || this.store.selectSnapshot(UserState.currentCustomer).customer?.customerNumber;

    if (data.moduleCode === ModuleCodeEnum.Loyalty) {
      if (this.permissionService.hasPermission(PermissionEnum.MenuPromotionPortal)) {
        return this.openPromotionPortal(data);
      }
    }
    if (data.moduleCode === ModuleCodeEnum.CatQR) {
      if (this.permissionService.hasPermission(PermissionEnum.EquipmentDetail)) {
        return this.openCatQRModule(data);
      }
    }

    if (data.moduleCode === ModuleCodeEnum.Custom) {
      return this.openCustomModule(data);
    }

    if (data.moduleCode === ModuleCodeEnum.Inner) {
      return this.openModuleInnerDetect(data);
    }

    if (data.moduleCode === ModuleCodeEnum.VisionLink) {
      return this.openVisionLinkModule(data);
    }

    if (data.moduleCode === ModuleCodeEnum.PCC) {
      return this.openPCCModule(data);
    }

    return this.findAndNavigate(customerNumber, data.moduleCode, {
      relativeUrl: data.url,
    }, data);
  }

  openServiceModule(customerNumber = null, serviceNumber?: string, openServiceMap?: boolean, extras = {}) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    if (openServiceMap && serviceNumber) {
      const overrides = { relativeUrl: 'customer/service-list/map/' + serviceNumber, replaceUrl: true };
      return this.findAndNavigate(customerNumber, ModuleCodeEnum.Service, overrides);
    } else if (!openServiceMap && serviceNumber) {
      const overrides: any = { serviceNumber, ...extras };
      return this.findAndNavigate(customerNumber, ModuleCodeEnum.Service, overrides);
    }

    return this.findAndNavigate(customerNumber, ModuleCodeEnum.Service);
  }

  openPromotionPortal(data: any = null) {
    this.store.select(UserState.currentCustomer)
      .pipe(filter(Boolean), take(1))
      .subscribe(() => {
        this.customerService.getOTT().subscribe(ott => {
          const searchParams = new URLSearchParams();
          searchParams.append('ott', ott.oneTimeToken);
          if (data?.path) {
            searchParams.append('path', data?.path);
          }
          this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
            url: environment.frameUrl + '/customer/loyality?' + searchParams.toString(),
            title: this.translateService.instant('_promotion_portal'),
            showProgress: false,
            closeButton: true,
          });
        });
      });
  }

  openLiveVideoByQueue(queue = null, callType = null) {
    this.openVideoCall('/live-support/digital-banko?'
      + (queue ? ('queueName=' + queue + '&') : '')
      + (queue ? ('callType=' + callType + '&') : '') + 'ott={OTT}');
  }

  openVideoCall(urlPath = '/digital-banko') {
    this.store.select(UserState.currentCustomer)
      .pipe(filter(Boolean), take(1))
      .subscribe(() => {
        this.customerService.getOTT().subscribe(ott => {
          this.frameMessageService.sendMessage(FrameMessageEnum.openAssistBox, {
            url: environment.frameUrl + urlPath.replace('{OTT}', ott.oneTimeToken)
          });
        });
      });
  }

  openFinancialModule(customerNumber = null, toMutabakat?: boolean) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    const overrides: any = {};

    if (toMutabakat) {
      overrides.relativeUrl = 'customer/mutabakatlar/';
      overrides.replaceUrl = true;
    }

    return this.findAndNavigate(customerNumber, ModuleCodeEnum.Finance, overrides);
  }

  openQuotationModule(customerNumber = null, quotationNumber = null) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    if (quotationNumber) {
      const path = {
        params: {
          quotationNumber
        }
      };
      return this.findAndNavigate(customerNumber, ModuleCodeEnum.Quotation, path);
    }
    return this.findAndNavigate(customerNumber, ModuleCodeEnum.Quotation);
  }

  openEquipmentModule(customerNumber: any, equipmentNumber: any, extras = {}) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    const overrides: any = { ...extras };

    if (equipmentNumber) {
      overrides.relativeUrl = 'customer/equipment-detail/' + equipmentNumber;
    }

    return this.findAndNavigate(customerNumber, ModuleCodeEnum.Equipment, overrides);
  }

  openSparePartModule(customerNumber: any = null, options: any = {}) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    const customer = this.findCustomerWithModule(customerNumber, ModuleCodeEnum.SparePart);
    if (!customer) {
      console.log('ERROR: customer not found');
      return;
    }
    this.store.dispatch(new ChangeCustomerAction(customer));
    const currentCustomer = this.store.selectSnapshot(UserState.currentCustomer);
    const module = customer.modules.find((item) => item.code === ModuleCodeEnum.SparePart);
    console.log('GETTING OTT');
    this.customerService.getOTT().subscribe(ott => {
      let url = module.url + '?ott=' + ott.oneTimeToken;
      if (options.query) {
        url += '&' + options.query;
      }

      this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
        url,
        title: module.name,
        currentCustomer,
        isSparePart: true,
        listenEvent: true,
      });
    });
    return;
  }

  openPCCModuleOld(customerNumber: any = null, extras = {}) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }

    return this.findAndNavigate(customerNumber, ModuleCodeEnum.PCC);
  }

  openExpertiseForm() {
    this.store.dispatch(new HeaderStatusAction({ closeModal: true }));
    this.openModuleInner({
      relativeUrl: '/expertise/init',
      pageTitle: this.translateService.instant('_expertise_form'),
    });
  }

  openBoomGuruForm(options: { boomGuruId?: string } = {}) {
    this.openModuleInner({
      relativeUrl: options?.boomGuruId ? '/boom-guru/detail/' + options?.boomGuruId : '/boom-guru/init',
      // pageTitle: this.translateService.instant('_boom_guru'),
      pageTitle: '',
      previous: {
        headerStatus: this.store.selectSnapshot(CustomerState.header),
        navigate: '/',
      },
    }, !!options?.boomGuruId);
    setTimeout(() =>
      this.store.dispatch(new HeaderStatusAction({
        img: {
          src: 'assets/boom_guru.svg',
          alt: ''
        },
        titleContentStyle: 'min-width: 155px;',
      })), 1000
    );
  }

  openSurveyForm(data: any = {}, customerNumber = null) {
    const actions = [];
    if (customerNumber) {
      actions.push(new FindAndChangeCustomerAction(customerNumber));
    }
    this.store.dispatch(actions).subscribe(() => {
      this.store.dispatch(new HeaderStatusAction({ closeModal: true }));
      this.openModuleInner({
        relativeUrl: data?.link || '/survey',
        pageTitle: this.translateService.instant('_surveys'),
        previous: {
          headerStatus: this.store.selectSnapshot(CustomerState.header),
          navigate: '/',
        },
      });
    });
  }

  openSparePartRequestForm() {
    this.openModuleInner({
      relativeUrl: '/form/request-equipment-part',
      pageTitle: this.translateService.instant('_spare_part_request'),
    });
  }

  openMDAForm() {
    this.openModuleInner({
      relativeUrl: '/form/request-mda',
      pageTitle: this.translateService.instant('_mda_request'),
    });
  }

  openServiceForm(params) {
    this.openModuleInner({
      relativeUrl: '/form/request-service',
      pageTitle: this.translateService.instant('_service_request'),
      params
    });
  }

  openCompanyInformations() {
    this.openModuleInner({
      relativeUrl: '/customer/company-informations',
      pageTitle: this.translateService.instant('_company_informations'),
      backButton: true,
      closeButton: false,
    });
  }

  openModuleInner(data: Partial<IframeStateModel & { relativeUrl: string }>, replaceUrl = false) {
    const url = data.relativeUrl?.startsWith('http')
      ? data.relativeUrl : environment.frameUrl + (data.relativeUrl.startsWith('/') ? data.relativeUrl : ('/' + data.relativeUrl));
    const iframeAction: Partial<IframeStateModel> = {
      url,
      params: {
        showHeader: false,
        navigatedPage: 'Contact',
      },
      active: true,
      closeButton: true,
      backButton: false,
      // pageTitle: data.pageTitle,
      previous: {
        headerStatus: this.store.selectSnapshot(CustomerState.header),
        navigate: this.location.path(),
      },
      ...data
    };
    this.router.navigate(['module', uuidv4()], { state: { iframeAction }, replaceUrl }).then();
  }

  protected maintenanceModeProcess(module: any) {
    const message = module?.maintenanceMessage || this.translateService.instant('_module_maintenance_message');
    throwError(message).subscribe();
    this.modalService.errorModal({
      message
    });
  }

  openEquipmentAgenda(customerNumber: any, equipmentNumber: any, extras = {}) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    const overrides: any = { ...extras };
    if (equipmentNumber) {
      overrides.relativeUrl = `form/request-my-equipment-agenda/${equipmentNumber}/calendar`;
    }

    return this.findAndNavigate(customerNumber, ModuleCodeEnum.Equipment, overrides);
  }

  openServiceChat(customerNumber: string, serviceNumber: string, extras = {}) {
    if (!customerNumber) {
      customerNumber = this.getCurrentCustomerNumber();
    }
    const overrides: any = { ...extras };

    if (serviceNumber) {
      overrides.relativeUrl = `customer/work-order/chat/${serviceNumber}`;
    }

    return this.findAndNavigate(customerNumber, ModuleCodeEnum.Service, overrides);
  }

  protected openCatQRModule(data: OpenLinkData) {
    this.store.dispatch(new UpdateUserLoadingAction(true));
    this.store.dispatch(new CatQRAction(data.url)).pipe(
      map(() => this.store.selectSnapshot(CustomerState.catQr))).subscribe(state => {
        if (this.store.selectSnapshot(UserState.getState)?.isLdapLogin) {
          //apiden gelen customerNumber ile customerDetail apisine çıkıp rpId'yi buluyorum. rpId catQR apisinden gelmiyor
          this.store.dispatch(new CustomerDetailAction(state?.customerNumber)).pipe(
            map(() => this.store.selectSnapshot(CustomerState.customer))).subscribe(states => {
              this.store.dispatch(new BorusanUserGetRelatedPersonsAction(states?.id)).pipe(
                map(() => this.store.selectSnapshot(BorusanUserState.getBorusanRelatedPersons))).subscribe((res: any) => {
                  this.store.dispatch(new BorusanUserSetSelectedCustomerAction(
                    state?.companyId,
                    states?.id,
                    res?.relatedPersons?.[0]?.id
                  )).subscribe(() => {
                    this.openEquipmentModule(state?.customerNumber, state?.equipmentNumber);
                  });
                },
                error => {
                  this.store.dispatch(new UpdateUserLoadingAction(false));
                }
              );
            },
            error => {
              this.store.dispatch(new UpdateUserLoadingAction(false));
            }
          );
        } else {
          this.store.dispatch(new UpdateUserLoadingAction(false));
          this.openEquipmentModule(state?.customerNumber, state?.equipmentNumber);
        }
      },
      error => {
        this.store.dispatch(new UpdateUserLoadingAction(false));
      }
    );
  }

  protected getCurrentCustomerNumber() {
    return this.store.selectSnapshot(UserState.currentCustomer)?.customer?.customerNumber;
  }

  openCustomModule(data: any) {
    this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
      url: data.url,
      title: data.title,
      showProgress: false,
      closeButton: true,
    });
  }

  protected openModuleInnerDetect(data: Partial<OpenLinkData & { code: string, title: string }>) {
    if (data.code) {
      const code = data.code.charAt(0).toUpperCase() + data.code.slice(1);
      const methodName = `open${code}Form`;
      if (this[methodName]) {
        console.log('data', data);
        return this[methodName](data);
      }
    }

    return this.openModuleInner({
      relativeUrl: data?.link,
      pageTitle: data?.title ? this.translateService.instant(data?.title) : null,
    });
  }

  openExternalModule(module: any) {
    if (module?.tags?.find(tag => tag === 'externalview')) {
      // const user = this.store.selectSnapshot(UserState.getState);
      const frameOptions: any = { ...(module.frameOptions || {}) };
      const currentCustomer = this.store.selectSnapshot(UserState.currentCustomer);
      console.log('GETTING OTT');
      if (module.code === ModuleCodeEnum.SparePart) {
        this.store.dispatch(new PageOpenedAction(PagesEnum.sparePartPage));
        frameOptions.meta = {
          adid: this.store.selectSnapshot(CommonState.adid),
          adjustToken: this.store.selectSnapshot(CommonState.adjustToken),
        };
      }
      if (module.code === ModuleCodeEnum.PCC) {
        frameOptions.pccDigitalBanko = this.systemFeatureCatPccVideoCall();
      }

      if (module.code === ModuleCodeEnum.SIS) {
        frameOptions.pccDigitalBanko = this.systemFeatureCatSisVideoCall();
      }

      if (module.id === '4b15911e-f0ea-41de-88fc-bb75e4ccffc0 ') {
        module.disabled = true;
        return this.frameMessageService.startBackgroundFrame({
          url: module.url,
        });
      }


      const withOtt = typeof module.withOtt !== 'undefined' ? module.withOtt : true;

      this.store.dispatch(withOtt ? new GetOTTAction() : [])
        .subscribe(() => {
          let url = module.url;
          if (withOtt) {
            const ott = this.store.selectSnapshot(CustomerState.ott);
            // url += '?ott=' + ott.oneTimeToken;
            url = this.addUrlParams(url, 'ott', ott.oneTimeToken);
          }

          if (frameOptions?.languageKey) {
            url = this.addUrlParams(url, module.frameOptions?.languageKey, this.store.selectSnapshot(LoginState.language));
          }

          this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
            url,
            title: module.name,
            // user,
            currentCustomer,
            isSparePart: module.code === ModuleCodeEnum.SparePart,
            listenEvent: true,
            ...frameOptions
          });
          // window.location.href = module.url + '?ott=' + ott.oneTimeToken;
        });

      return;
    }
  }

  protected addUrlParams(url: string, key, value) {
    return url + (url.includes('?') ? '&' : '?') + `${key}=${value}`;
  }

  private systemFeatureCatPccVideoCall() {
    const features = this.store.selectSnapshot(SettingsState.systemFeatures);
    return systemFeature('cat_pcc_video_call', features, false);
  }

  private systemFeatureCatSisVideoCall() {
    const features = this.store.selectSnapshot(SettingsState.systemFeatures);
    return systemFeature('cat_sis_video_call', features, false);
  }

  private systemFeatureCatVisionLinkVideoCall() {
    const features = this.store.selectSnapshot(SettingsState.systemFeatures);
    return systemFeature('cat_vision_link_video_call', features, false);
  }

  private openVisionLinkModule(data: OpenLinkData) {
    this.openInPCC({
      title: this.translateService.instant('Cat® VisionLink'),
      url: SSORedirectKeys.VisionLink,
      pccDigitalBanko: this.systemFeatureCatVisionLinkVideoCall(),
      direct: true,
    });
  }

  openPCCModule(data: Partial<OpenLinkData> = {}) {
    const lang = this.store.selectSnapshot(LoginState.language);

    let url: string = data?.serial ? SSORedirectKeys.partsCatComLanding : SSORedirectKeys.partsCatCom;
    url = url.replace('{{LANG}}', lang);
    url = url.replace('{{LANG}}', lang); // double land
    if (data?.serial) {
      url = url.replace('{{SERIAL}}', data.serial);
    }

    this.openInPCC({
      title: this.translateService.instant('PartsCatCom'),
      url,
      pccDigitalBanko: this.systemFeatureCatPccVideoCall
    });
  }

  openInPCC(data) {
    if (!getCookie('CATSignInCode')) {
      // not logged in to PCC
      if (!data.direct) {
        data.url = SSORedirectKeys.partsCatComInternal
          .replace('{{CAT_INTERNAL_REDIRECT}}', encodeURIComponent(data.url));
      }

      data.url = environment.appUrl + '/auth/sso?ru=' + encodeURIComponent(data.url);
    }

    this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
      ...data,
      closeButton: true,
      loadingPage: true,
    });
  }
}
