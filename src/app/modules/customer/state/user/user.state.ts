import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { ChangeCustomerAction, FindAndChangeCustomerAction, UpdateUserLoadingAction, UserAction, } from './user.actions';
import { BorusanUserLdapData, UserModel } from '../../../authentication/model/user.model';
import { BorusanBlockedActionsAction, UserPermissionAction } from '../../../../shared/state/settings/settings.actions';
import { CustomerRelationModel } from '../../model/customer-relation.model';
import { of, throwError } from 'rxjs';
import { catchError, delay, tap } from 'rxjs/operators';
import { UserService } from '../../service/user.service';
import { Injectable } from '@angular/core';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { ActiveApplicationsModel, MeResponse } from '../../response/me.response';
import { CommonState } from '../../../../shared/state/common/common.state';
import { AgreementModel, ApprovalAgreementModel } from '../../../definition/model/agreement.model';
import { Navigate } from '@ngxs/router-plugin';
import { BorusanUserSetSelectedCustomerAction } from 'src/app/modules/borusan-user/state/borusan-user.action';
import { BorusanUserService } from 'src/app/modules/borusan-user/service/borusan-user.service';
import { TranslateService } from '@ngx-translate/core';
import { ModuleCodeEnum } from '../../../../shared/enum/module-code.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { ModuleModel } from '../../model/module.model';
import { SSORedirectKeys } from '../../../../shared/enum/sso-pats.enum';
import { environment } from '../../../../../environments/environment';
import { LoginState } from '../../../authentication/state/login/login.state';
import { UpdateAppStorageAction } from '../../../../shared/state/common/common.actions';

export interface UserStateModel extends UserModel {
  customerRelationsGrouped: CustomerRelationModel[];
  activeApplications: ActiveApplicationsModel[];
  userLoading: boolean;
  customers: SanitizedCustomerModel[];
  currentCustomer: SanitizedCustomerModel;
  agreements: AgreementModel[];
  approvalAgreements: ApprovalAgreementModel[];
  lastPull: number;
}

@State<UserStateModel>({
  name: 'user',
  defaults: {
    id: null,
    firstName: null,
    lastName: null,
    email: null,
    mobile: null,
    username: null,
    isGdprApproved: true,
    customerRelationsGrouped: [],
    activeApplications: [],
    userLoading: false,
    customers: [],
    currentCustomer: null,
    agreements: [],
    approvalAgreements: [],
    isLdapLogin: false,
    isLdapForceSearchCustomer: false,
    isMobileVerified: false,
    forceMobileVerify: false,
    lastPull: 0,
  },
})
@Injectable()
export class UserState {
  protected userRefreshTime = 180000;

  constructor(
    private readonly userService: UserService,
    private readonly store: Store,
    private readonly borusanUserService: BorusanUserService,
    private readonly translateService: TranslateService,
    private readonly frameMessageService: FrameMessageService
  ) { }

  @Selector()
  public static getState(state: UserStateModel) {
    return state;
  }

  @Selector()
  public static id({ id }: UserStateModel): string {
    return id;
  }

  @Selector()
  public static firstName({ firstName }: UserStateModel): string {
    return firstName;
  }

  @Selector()
  public static lastName({ lastName }: UserStateModel): string {
    return lastName;
  }

  @Selector()
  public static email({ email }: UserStateModel): string {
    return email;
  }

  @Selector()
  public static mobile({ mobile }: UserStateModel): string {
    return mobile;
  }

  @Selector()
  public static username({ username }: UserStateModel): string {
    return username;
  }

  @Selector()
  public static basics({ id, firstName, lastName, email, mobile, username, currentCustomer }: UserStateModel): any {
    return {
      id, firstName, lastName, email, mobile, username, customer: {
        name: currentCustomer.customer?.name,
        countryCode: currentCustomer.groupKey,
        cityName: currentCustomer.cityName,
        customerNumber: currentCustomer?.customer?.customerNumber,
        customerId: currentCustomer.customer?.id
      }
    };
  }

  @Selector()
  public static isGdprApproved({ isGdprApproved }: UserStateModel): boolean {
    return isGdprApproved;
  }

  @Selector()
  public static agreements({ agreements }: UserStateModel): AgreementModel[] {
    return agreements;
  }

  @Selector()
  public static approvalAgreements({ agreements }: UserStateModel): AgreementModel[] {
    return agreements;
  }

  @Selector()
  public static customerRelationsGrouped({
    customerRelationsGrouped,
  }: UserStateModel): CustomerRelationModel[] {
    return customerRelationsGrouped;
  }

  @Selector()
  public static customers({
    customers,
  }: UserStateModel): SanitizedCustomerModel[] {
    return customers;
  }

  @Selector()
  public static currentCustomer({
    currentCustomer,
  }: UserStateModel): SanitizedCustomerModel {
    return currentCustomer;
  }

  @Selector()
  public static userLoading({ userLoading }: UserStateModel): boolean {
    return userLoading;
  }

  @Selector()
  public static activeApplications({
    activeApplications,
  }: UserStateModel): ActiveApplicationsModel[] {
    return activeApplications;
  }

  @Selector()
  public static isBorusanUser({
    isLdapLogin,
  }: UserStateModel): boolean {
    return isLdapLogin;
  }

  @Selector()
  public static borusanUserData({
    ldapFullname, ldapUsername, isLdapLogin, isLdapForceSearchCustomer
  }: UserStateModel): BorusanUserLdapData {
    return { ldapFullname, ldapUsername, isLdapLogin, isLdapForceSearchCustomer };
  }

  @Selector()
  public static isMobileVerified({
    isMobileVerified,
  }: UserStateModel): boolean {
    return isMobileVerified;
  }

  @Selector()
  public static forceMobileVerify({
    forceMobileVerify,
  }: UserStateModel): boolean {
    return forceMobileVerify;
  }

  @Action(UserAction)
  public userAction(
    { patchState, getState }: StateContext<UserStateModel>,
    userAction: UserAction
  ) {
    if (!userAction.forceReload && new Date().getTime() - getState().lastPull < this.userRefreshTime) {
      return of('');
    }

    if (getState().userLoading) {
      return of('');
    }
    patchState({
      userLoading: userAction.showLoading,
    });

    return this.userService.getMe().pipe(
      tap((value) => {
        // ? isLdapForceSearchCustomer yönlendirmesi
        if (value?.isLdapForceSearchCustomer) {
          this.store.dispatch(new Navigate(['borusan'], { forceLdpa: true }));
        }
        const customers = this.companyManagement(value);
        const founded = this.findCurrentCustomer(customers);
        const current = getState().currentCustomer;
        patchState({
          userLoading: false,
          id: value?.id,
          firstName: value?.firstName,
          lastName: value?.lastName,
          email: value?.email.toLowerCase(),
          mobile: value?.mobile,
          username: value?.username,
          isGdprApproved: value?.isGdprApproved,
          customerRelationsGrouped: value?.customerRelationsGrouped,
          activeApplications: value?.activeApplications,
          customers,
          currentCustomer: JSON.stringify(founded) !== JSON.stringify(current) ? founded : current,
          agreements: value.agreements,
          isLdapLogin: value?.isLdapLogin,
          isLdapForceSearchCustomer: value?.isLdapForceSearchCustomer,
          ldapFullname: value?.ldapFullname,
          ldapUsername: value?.ldapUsername,
          isMobileVerified: value?.isMobileVerified,
          forceMobileVerify: value?.forceMobileVerify,
          lastPull: new Date().getTime(),
        });
        this.frameMessageService.sendMessage(FrameMessageEnum.customerFinishLoad);
        if (getState().isLdapLogin) {
          this.store.dispatch(new BorusanBlockedActionsAction());
        }
      }),
      catchError((err) => {
        patchState({
          userLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(ChangeCustomerAction)
  public changeCustomerAction(
    { patchState, getState }: StateContext<UserStateModel>,
    { customer }: ChangeCustomerAction
  ) {
    patchState({
      currentCustomer: customer,
    });
  }

  @Action(UpdateUserLoadingAction)
  updateLoading(
    { getState, patchState }: StateContext<UserStateModel>,
    { loading }: UpdateUserLoadingAction
  ): void {
    patchState({
      userLoading: loading,
    });
  }

  @Action(BorusanUserSetSelectedCustomerAction)
  setBorusanUserCustomerSelect(
    { getState, patchState }: StateContext<UserStateModel>,
    { companyId, customerId, customerRelatedPersonId }: BorusanUserSetSelectedCustomerAction) {
    patchState({
      userLoading: true,
    });
    return this.borusanUserService.setBorusanCustomerSelect(companyId, customerId, customerRelatedPersonId)
      .pipe(
        tap((value) => {
          const customers = this.companyManagement(value);
          const founded = this.findCurrentCustomer(customers);
          const current = getState().currentCustomer;
          this.store.dispatch(new UserPermissionAction());
          patchState({
            userLoading: false,
            id: value?.id,
            firstName: value?.firstName,
            lastName: value?.lastName,
            email: value?.email.toLowerCase(),
            mobile: value?.mobile,
            username: value?.username,
            isGdprApproved: value?.isGdprApproved,
            customerRelationsGrouped: value?.customerRelationsGrouped,
            activeApplications: value?.activeApplications,
            customers,
            currentCustomer: JSON.stringify(founded) !== JSON.stringify(current) ? founded : current,
            agreements: value.agreements,
            isLdapLogin: value?.isLdapLogin,
            isLdapForceSearchCustomer: value?.isLdapForceSearchCustomer,
          });
        }),
        catchError((err) => {
          patchState({
            userLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  protected companyManagement(data: MeResponse) {
    let index = 0;
    const passiveCompanies = [];
    if (data.activeApplications) {
      data.activeApplications.map((item) => {
        passiveCompanies.push({
          ...item,
          name: item.customerCompanyName,
          displayName: item.customerCompanyName,
          groupTitle: item.customerCompanyName,
          // displayName: '(' + item.countryCode + ') ' + item.customerCompanyName,
          passive: true,
          groupKey: item.countryCode,
          index,
          modules: [],
          roleList: []
        });
        index++;
      });
    }

    const arr = [];
    data.customerRelationsGrouped?.map((item) => {
      const exist = arr.findIndex((x) => x.customer.name === item.customer.name);
      const customerRelation = data.customerRelations.find(i => i.customer.id === item.customer.id);

      if (exist > -1) {
        arr[exist].duplicate = true;
      }


      arr.push({
        ...item,
        index,
        duplicate: exist > -1,
        // passive: true,
        name: item.customer.name,
        displayName: '(' + item.groupKey + ') ' + item.customer.name,
        modules: [
          ...(item.modules || []),
          ...this.extraLocalModules(item)
        ],
        roleList: customerRelation?.roleList || [],
      });
      index++;
    });

    return [...arr, ...passiveCompanies];
  }

  findCurrentCustomer(companies) {
    const appStorage = this.store.selectSnapshot(CommonState.appStorage);
    if (appStorage?.currentCustomerTitle) {
      const found = companies.find((x) => x.groupTitle === appStorage.currentCustomerTitle);
      if (found) {
        return found;
      }
    }

    const current = companies.find((x) => x.modules?.length > 0);
    if (!current) {
      return companies[0];
    }
    return current;
  }

  @Action(FindAndChangeCustomerAction)
  public findAndChangeCustomerAction(
    { patchState, getState, dispatch, }: StateContext<UserStateModel>,
    { customerNumber }: FindAndChangeCustomerAction
  ) {
    const customer = getState().customers.find((group) => {
      return group?.customer?.customerNumber === customerNumber;
    });

    if (!customer) { //TODO
      return of();
    }
    if (customer.customer?.customerNumber !== getState().currentCustomer?.customer?.customerNumber) {
      patchState({
        userLoading: true,
      });
      return dispatch(new ChangeCustomerAction(customer))
        // .pipe(delay(5000))
        .pipe(tap(() => {
          patchState({
            userLoading: false,
          });

          dispatch(new UpdateAppStorageAction({
            currentCustomerTitle: customer.groupTitle
          }));
        }));
    }

    return of();
  }

  protected extraLocalModules(item: CustomerRelationModel): ModuleModel[] {
    if (!this.store.selectSnapshot(LoginState.isSAMLLogin)) {
      return [];
    }

    return [
      // {
      //   ...item.modules[item.modules.length - 1],
      //   url: null,
      //   id: '719f75fc-3171-48c0-a88c-c322d6691c4a',
      //   code: ModuleCodeEnum.LiveSupport,
      //   imageUrl: 'assets/live-support.png',
      //   name: this.translateService.instant('_live_support_module'),
      //   tags: []
      // },
      {
        ...item.modules[item.modules.length - 1],
        url: environment.appUrl + '/auth/sso?ru=' + encodeURIComponent(SSORedirectKeys.partsCatCom),
        id: '719f75fc-3171-48c0-a88c-c322d6691c4a',
        code: ModuleCodeEnum.PCC,
        imageUrl: 'assets/images/cat-logo.png',
        name: this.translateService.instant('PartsCatCom'),
        tags: ['externalview'],
        // tags: ['redirect'],
        withOtt: false,
        frameOptions: {
          closeButton: true,
          onCloseWarning: true,
          loadingPage: true,
          languageKey: 'language',
          withLanguage: true
        }
      },
      {
        ...item.modules[item.modules.length - 1],
        url: environment.appUrl + '/auth/sso?ru=' + encodeURIComponent(SSORedirectKeys.sis2),
        id: '0af69bc7-20f1-4889-94b5-2124f3955e08',
        code: ModuleCodeEnum.SIS,
        imageUrl: 'assets/images/cat-sis.png',
        name: this.translateService.instant('SIS 2.0'),
        tags: ['externalview'],
        // tags: ['redirect'],
        withOtt: false,
        frameOptions: {
          closeButton: true,
          loadingPage: true,
          languageKey: 'language'
        }
      },
      // {
      //   ...item.modules[item.modules.length - 1],
      //   url: environment.appUrl + '/auth/sso?ru=' + encodeURIComponent(SSORedirectKeys.partsCatCom),
      //   id: '4b15911e-f0ea-41de-88fc-bb75e4ccffc0 ',
      //   code: ModuleCodeEnum.PCC,
      //   imageUrl: 'assets/images/avatar.jpg',
      //   name: this.translateService.instant('Background Login'),
      //   tags: ['externalview'],
      //   // tags: ['redirect'],
      //   withOtt: false,
      //   frameOptions: {
      //     closeButton: true,
      //     onCloseWarning: true,
      //     loadingPage: true,
      //     languageKey: 'language',
      //     withLanguage: true
      //   }
      // },
    ];
  }
}
