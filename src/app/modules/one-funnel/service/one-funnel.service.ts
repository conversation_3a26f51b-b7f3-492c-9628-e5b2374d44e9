import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OneFunnelService {

  constructor(
    private readonly http: HttpClient,
  ) { }

}
