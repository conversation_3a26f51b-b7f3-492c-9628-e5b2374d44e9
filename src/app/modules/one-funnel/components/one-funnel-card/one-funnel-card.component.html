<div class="one-funnel-card" *ngIf="insight">
  <div class="one-funnel-card-header">
    <div class="d-flex align-items-center">
      <i class="icon" [ngClass]="getIconClass()"></i>
      <span class="ml-2" style="font-weight: 500; font-size: 13px">
        {{ insight.insightTitle }}
      </span>
    </div>
    <button class="btn p-0" style="height: 20px">
      <i class="icon icon-dot3"></i>
    </button>
  </div>
  <div class="equipment-detail">
    <i class="icon icon-excavator"></i>
    <span class="ml-2">{{ insight.serialNumber }}</span>
  </div>

  <div class="one-funnel-date">
    <span>{{ insight.insightDate |date: 'shortDate'  }}</span>

    <div
      class="one-funnel-date-badge"
      style="
        background-color: #fff1f1;
        border-radius: 5px;
        padding: 0.15rem 0.25rem;
      "
    >
      <span style="color: #d10000; font-size: 10px"> due in {{ insight.activeDays }} days </span>
    </div>
  </div>
  <div class="action-btns row">
    <button
      class="col btn btn-sm btn-warning text-white"
      style="padding: 5px; display: flex; align-items: center; gap: 0.25rem"
    >
      <i class="icon icon-go-to-equipment"></i>
      {{ "_go_to_equipment" | translate }}
    </button>
    <button
      *ngIf="shouldShowSmuButton()"
      class="col btn btn-sm btn-warning text-white"
      style="padding: 5px; display: flex; align-items: center; gap: 0.25rem"
    >
      <i class="icon icon-enter-smu"></i>
      {{ "_enter_smu" | translate }}
    </button>
  </div>
</div>
