import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-one-funnel-card',
  templateUrl: './one-funnel-card.component.html',
  styleUrls: ['./one-funnel-card.component.scss'],
})
export class OneFunnelCardComponent implements OnInit {
  constructor() {}

  ngOnInit() {}

  getIconClass(iconName?: string): string {
    switch (iconName) {
      case 'edit':
        return 'icon-edit';
      case 'delete':
        return 'icon-delete';
      case 'add':
        return 'icon-add';
      case 'save':
        return 'icon-save';
      default:
        return 'icon-smu-reading';
    }
  }
}
