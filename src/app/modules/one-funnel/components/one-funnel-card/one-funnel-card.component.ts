import { Component, Input, OnInit } from '@angular/core';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';

@Component({
  selector: 'app-one-funnel-card',
  templateUrl: './one-funnel-card.component.html',
  styleUrls: ['./one-funnel-card.component.scss'],
})
export class OneFunnelCardComponent implements OnInit {
  @Input() insight: OneFunnelInsightModel;

  constructor() {}

  ngOnInit() {}

  getIconClass(iconName?: string): string {
    switch (iconName) {
      case 'edit':
        return 'icon-edit';
      case 'delete':
        return 'icon-delete';
      case 'add':
        return 'icon-add';
      case 'save':
        return 'icon-save';
      default:
        return 'icon-smu-reading';
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    };

    return date.toLocaleDateString('en-US', options);
  }

  shouldShowSmuButton(): boolean {
    return this.insight?.flowKey === 'ONE_ST_004';
  }
}
