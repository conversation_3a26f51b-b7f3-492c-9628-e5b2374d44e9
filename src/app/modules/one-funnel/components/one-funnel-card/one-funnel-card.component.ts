import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { SetOneFunnelDetailAction } from '../../state/one-funnel.actions';

@Component({
  selector: 'app-one-funnel-card',
  templateUrl: './one-funnel-card.component.html',
  styleUrls: ['./one-funnel-card.component.scss'],
})
export class OneFunnelCardComponent implements OnInit {
  @Input() insight: OneFunnelInsightModel;
  @Input() isDetailView: boolean = false;

  constructor(
    private readonly customerModuleService: CustomerModuleService,
    private readonly store: Store,
    private readonly router: Router
  ) {}

  ngOnInit() {}

  getIconClass(iconName?: string): string {
    switch (iconName) {
      case 'edit':
        return 'icon-edit';
      case 'delete':
        return 'icon-delete';
      case 'add':
        return 'icon-add';
      case 'save':
        return 'icon-save';
      default:
        return 'icon-smu-reading';
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    };

    return date.toLocaleDateString('en-US', options);
  }

  shouldShowSmuButton(): boolean {
    return this.insight?.flowKey === 'ONE_ST_004';
  }

  shouldShowGoToEquipmentButton(): boolean {
    return !!this.insight?.SerialNumber;
  }

  onCardClick(): void {
    if (!this.isDetailView && this.insight) {
      this.store.dispatch(new SetOneFunnelDetailAction(this.insight));
      this.router.navigate(['/one-funnel/detail']);
    }
  }

  onGoToEquipment(): void {
    if (this.insight?.EquipmentNumber) {
      this.customerModuleService.openEquipmentModule(
        this.insight.CustomerNumber,
        this.insight.EquipmentNumber
      );
    }
  }
}
