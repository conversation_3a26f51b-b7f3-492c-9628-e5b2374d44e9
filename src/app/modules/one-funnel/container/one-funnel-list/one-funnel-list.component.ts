import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { TranslatePipe } from '@ngx-translate/core';
import { CustomerModel } from 'src/app/shared/models/customer.model';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';

@Component({
  selector: 'app-one-funnel-list',
  templateUrl: './one-funnel-list.component.html',
  styleUrls: ['./one-funnel-list.component.scss'],
})
export class OneFunnelListComponent implements OnInit, OnDestroy {
  @Select(CustomerState.customer) customer$: Observable<CustomerModel>;
  customer: CustomerModel;

  showSubmitModal = false;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly translatePipe: TranslatePipe
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        closeButton: true,
        hamburgerMenu: true,
        notificationIcon: false,
        title: this.translatePipe.transform('_one_funnel_insights'),
      })
    );

    this.customer$.subscribe((customer) => {
      if (customer) {
        console.log(customer);
        this.customer = customer;
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onClickItem(item: any): void {
    // Navigate to the detail page
    // this.router.navigate([
    //   '/',
    //   ...environment.rootUrl.split('/'),
    //   'boom-guru', 'detail', item.guruRequestId
    // ]);
  }

  openSubmitModal(): void {
    this.showSubmitModal = true;
  }

  closeSubmitModal(): void {
    this.showSubmitModal = false;
  }

  navigateToBack() {
    window.history.back();
  }
}
