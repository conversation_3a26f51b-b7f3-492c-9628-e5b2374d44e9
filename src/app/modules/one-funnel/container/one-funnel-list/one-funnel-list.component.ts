import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { TranslatePipe } from '@ngx-translate/core';
import { CustomerModel } from 'src/app/shared/models/customer.model';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { OneFunnelService } from '../../service/one-funnel.service';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';

@Component({
  selector: 'app-one-funnel-list',
  templateUrl: './one-funnel-list.component.html',
  styleUrls: ['./one-funnel-list.component.scss'],
})
export class OneFunnelListComponent implements OnInit, OnDestroy {
  @Select(CustomerState.customer) customer$: Observable<CustomerModel>;
  customer: CustomerModel;
  insights: OneFunnelInsightModel[] = [];
  loading = false;

  showSubmitModal = false;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly translatePipe: TranslatePipe,
    private readonly oneFunnelService: OneFunnelService
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        closeButton: true,
        hamburgerMenu: true,
        notificationIcon: false,
        title: this.translatePipe.transform('_one_funnel_insights'),
      })
    );

    this.customer$.subscribe((customer) => {
      if (customer) {
        console.log('CUST',customer);
        this.customer = customer;
        this.loadInsights();
      }
    });
  }

  loadInsights(): void {
    this.loading = true;
    this.oneFunnelService.getInsights()
      .pipe(takeUntil(this.subscriptions$))
      .subscribe({
        next: (insights) => {
          this.insights = insights;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading insights:', error);
          this.loading = false;
        }
      });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onClickItem(item: any): void {
    // Navigate to the detail page
    // this.router.navigate([
    //   '/',
    //   ...environment.rootUrl.split('/'),
    //   'boom-guru', 'detail', item.guruRequestId
    // ]);
  }

  openSubmitModal(): void {
    this.showSubmitModal = true;
  }

  closeSubmitModal(): void {
    this.showSubmitModal = false;
  }

  navigateToBack() {
    window.history.back();
  }
}
